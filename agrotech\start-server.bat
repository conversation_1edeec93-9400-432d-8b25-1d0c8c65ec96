@echo off
title Agrotech Server Launcher
color 0A
echo.
echo ========================================
echo    AGROTECH SERVER LAUNCHER
echo    Agricultural B2B Lead Generation
echo ========================================
echo.

:: Set the port for the server
set PORT=8080
set FALLBACK_PORT=3000

:: Get current directory
set CURRENT_DIR=%~dp0
cd /d "%CURRENT_DIR%"

echo Starting Agrotech website server...
echo Current directory: %CURRENT_DIR%
echo.

:: Check for Python and start Python server
echo [1/3] Checking for Python server...
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✓ Python found! Starting Python HTTP server on port %PORT%...
    echo.
    echo ========================================
    echo  SERVER RUNNING ON: http://localhost:%PORT%
    echo  Press Ctrl+C to stop the server
    echo ========================================
    echo.
    
    :: Open browser after a short delay
    timeout /t 2 /nobreak >nul
    start http://localhost:%PORT%
    
    :: Start Python server
    python -m http.server %PORT%
    goto :end
)

:: Check for Node.js and start Node server
echo [2/3] Checking for Node.js server...
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✓ Node.js found! Starting Node HTTP server on port %PORT%...
    echo.
    echo ========================================
    echo  SERVER RUNNING ON: http://localhost:%PORT%
    echo  Press Ctrl+C to stop the server
    echo ========================================
    echo.
    
    :: Open browser after a short delay
    timeout /t 2 /nobreak >nul
    start http://localhost:%PORT%
    
    :: Create and start simple Node server
    node -e "const http=require('http'),fs=require('fs'),path=require('path');http.createServer((req,res)=>{let filePath='.'+(req.url==='/'?'/index.html':req.url);const ext=path.extname(filePath).toLowerCase();const mimeTypes={'.html':'text/html','.js':'text/javascript','.css':'text/css','.json':'application/json','.png':'image/png','.jpg':'image/jpg','.gif':'image/gif'};fs.readFile(filePath,(err,content)=>{if(err){res.writeHead(404);res.end('File not found');}else{res.writeHead(200,{'Content-Type':mimeTypes[ext]||'application/octet-stream'});res.end(content,'utf-8');}});}).listen(%PORT%,()=>console.log('Server running at http://localhost:%PORT%/'));"
    goto :end
)

:: Check for PHP and start PHP server
echo [3/3] Checking for PHP server...
php --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✓ PHP found! Starting PHP built-in server on port %PORT%...
    echo.
    echo ========================================
    echo  SERVER RUNNING ON: http://localhost:%PORT%
    echo  Press Ctrl+C to stop the server
    echo ========================================
    echo.
    
    :: Open browser after a short delay
    timeout /t 2 /nobreak >nul
    start http://localhost:%PORT%
    
    :: Start PHP server
    php -S localhost:%PORT%
    goto :end
)

:: If no server is available, show instructions
echo.
echo ❌ No suitable server found!
echo.
echo To run the Agrotech website, please install one of the following:
echo.
echo 1. PYTHON (Recommended)
echo    - Download from: https://www.python.org/downloads/
echo    - Make sure to check "Add Python to PATH" during installation
echo.
echo 2. NODE.JS
echo    - Download from: https://nodejs.org/
echo.
echo 3. PHP
echo    - Download from: https://www.php.net/downloads/
echo.
echo After installation, run this script again.
echo.
echo Alternatively, you can:
echo - Open index.html directly in your browser (limited functionality)
echo - Use any other local web server of your choice
echo.

:end
echo.
echo ========================================
echo Server stopped. Press any key to exit.
echo ========================================
pause >nul
