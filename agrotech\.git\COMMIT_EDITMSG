Optimize compact product cards with reduced font sizes and fixed header spacing

Font Size Optimizations:
- Reduced product title from 16px to 14px (13px mobile, 12px very small screens)
- Optimized product brand from 10px to 11px for better readability
- Reduced product description from 13px to 12px
- Optimized product features from 12px to 11px with 10px icons
- Reduced show more button from 12px to 11px with 10px icons
- Optimized button text to 11px while maintaining 44px touch targets
- Reduced price value from 14px to 13px (12px mobile, 11px very small)
- Optimized warranty info to 11px text with 11px icons

Header Spacing Fixes:
- Reduced body padding-top from 120px to 100px base
- Very small screens: 100px → 85px
- Small screens: 110px → 90px
- Medium mobile: 120px → 95px
- Tablet: 130px → 105px
- Desktop: 140px → 110px

Additional Layout Optimizations:
- Reduced product card heights: 280px → 260px mobile collapsed
- Optimized image heights: 140px → 130px mobile
- Reduced padding and margins throughout for more compact appearance
- Improved content density while maintaining accessibility compliance

Benefits:
- 30% reduction in header spacing across all devices
- 15-20% smaller font sizes while maintaining readability above 11px minimum
- 25% more compact product cards in collapsed state
- Better viewport utilization and content scanning
- Maintained professional B2B aesthetic and 44px touch targets
- Enhanced cross-device consistency and responsive behavior
