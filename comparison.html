<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Comparison - a.agrotech | Compare Agricultural Equipment</title>
    <meta name="description" content="Compare agricultural equipment specifications, features, and prices side by side to make informed purchasing decisions.">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.html" class="logo">
                        <i class="fas fa-seedling"></i>
                        <span>a.agrotech</span>
                    </a>
                </div>
                
                <!-- Mobile Menu Toggle -->
                <button class="mobile-menu-toggle" aria-label="Toggle menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                
                <!-- Navigation Menu -->
                <div class="nav-menu">
                    <ul class="nav-links">
                        <li><a href="index.html">Home</a></li>
                        <li class="dropdown">
                            <a href="products.html">Products <i class="fas fa-chevron-down"></i></a>
                            <div class="dropdown-menu">
                                <div class="dropdown-section">
                                    <h4>Shop by Category</h4>
                                    <a href="products.html?category=tillage">Tillage Equipment</a>
                                    <a href="products.html?category=sowing">Sowing & Planting</a>
                                    <a href="products.html?category=protection">Crop Protection</a>
                                    <a href="products.html?category=harvesting">Harvesting</a>
                                </div>
                                <div class="dropdown-section">
                                    <h4>Shop by Brand</h4>
                                    <a href="products.html?brand=mahindra">Mahindra</a>
                                    <a href="products.html?brand=sonalika">Sonalika</a>
                                    <a href="products.html?brand=johndeere">John Deere</a>
                                </div>
                                <div class="dropdown-section">
                                    <h4>Shop by Crop</h4>
                                    <a href="products.html?crop=paddy">Paddy Solutions</a>
                                    <a href="products.html?crop=wheat">Wheat Solutions</a>
                                    <a href="products.html?crop=sugarcane">Sugarcane Solutions</a>
                                </div>
                            </div>
                        </li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="photo-gallery.html">Gallery</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                    
                    <!-- Search Bar -->
                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="Search equipment..." id="searchInput">
                        <button class="search-btn" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                        <div class="search-suggestions" id="searchSuggestions"></div>
                    </div>
                    
                    <!-- Contact Info -->
                    <div class="header-contact">
                        <a href="tel:1800-123-4567" class="toll-free">
                            <i class="fas fa-phone"></i>
                            <span>1800-123-4567</span>
                        </a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content">
                <h1>Product Comparison</h1>
                <p>Compare agricultural equipment specifications side by side</p>
                <nav class="breadcrumb">
                    <a href="index.html">Home</a>
                    <span>/</span>
                    <a href="products.html">Products</a>
                    <span>/</span>
                    <span>Comparison</span>
                </nav>
            </div>
        </div>
    </section>

    <!-- Comparison Section -->
    <section class="comparison-section">
        <div class="container">
            <!-- Comparison Header -->
            <div class="comparison-header">
                <div class="comparison-info">
                    <h2>Compare Products</h2>
                    <p>Select up to 3 products to compare their specifications and features</p>
                </div>
                <div class="comparison-actions">
                    <button class="btn btn-outline" onclick="clearComparison()">
                        <i class="fas fa-times"></i>
                        Clear All
                    </button>
                    <a href="products.html" class="btn btn-secondary">
                        <i class="fas fa-plus"></i>
                        Add More Products
                    </a>
                </div>
            </div>

            <!-- Product Selection -->
            <div class="product-selection" id="productSelection">
                <div class="selection-slot empty" onclick="openProductSelector(0)">
                    <div class="selection-placeholder">
                        <i class="fas fa-plus"></i>
                        <span>Add Product</span>
                    </div>
                </div>
                <div class="selection-slot empty" onclick="openProductSelector(1)">
                    <div class="selection-placeholder">
                        <i class="fas fa-plus"></i>
                        <span>Add Product</span>
                    </div>
                </div>
                <div class="selection-slot empty" onclick="openProductSelector(2)">
                    <div class="selection-placeholder">
                        <i class="fas fa-plus"></i>
                        <span>Add Product</span>
                    </div>
                </div>
            </div>

            <!-- Comparison Table -->
            <div class="comparison-table-container" id="comparisonTableContainer" style="display: none;">
                <div class="comparison-table" id="comparisonTable">
                    <!-- Comparison table will be generated dynamically -->
                </div>
            </div>

            <!-- No Products Message -->
            <div class="no-products-message" id="noProductsMessage">
                <div class="no-products-content">
                    <i class="fas fa-balance-scale"></i>
                    <h3>No Products Selected</h3>
                    <p>Add products to compare their specifications, features, and prices side by side.</p>
                    <a href="products.html" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        Browse Products
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Selector Modal -->
    <div class="modal" id="productSelectorModal">
        <div class="modal-content product-selector-modal">
            <div class="modal-header">
                <h3>Select Product to Compare</h3>
                <button class="modal-close" onclick="closeProductSelector()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="product-search">
                    <input type="text" class="form-input" placeholder="Search products..." id="productSearchInput">
                </div>
                <div class="product-list" id="productList">
                    <!-- Product list will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <a href="index.html" class="logo">
                            <i class="fas fa-seedling"></i>
                            <span>a.agrotech</span>
                        </a>
                        <p>Empowering Indian farmers with the right agricultural equipment and trusted dealer connections.</p>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="photo-gallery.html">Gallery</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Categories</h4>
                    <ul>
                        <li><a href="products.html?category=tillage">Tillage Equipment</a></li>
                        <li><a href="products.html?category=sowing">Sowing & Planting</a></li>
                        <li><a href="products.html?category=protection">Crop Protection</a></li>
                        <li><a href="products.html?category=harvesting">Harvesting</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-phone"></i> 1800-123-4567 (Toll Free)</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> New Delhi, India</p>
                    </div>
                    <div class="social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="#" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 a.agrotech. All rights reserved. | <a href="#">Privacy Policy</a> | <a href="#">Terms of Service</a></p>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Float Button -->
    <a href="https://wa.me/************" class="whatsapp-float" target="_blank" aria-label="Chat on WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/search.js"></script>
    <script>
        // Comparison functionality
        let selectedProducts = [null, null, null];
        let currentSlot = 0;
        let availableProducts = [];

        document.addEventListener('DOMContentLoaded', function() {
            loadAvailableProducts();
            checkURLParameters();
        });

        // Load available products
        async function loadAvailableProducts() {
            try {
                const response = await fetch('data/products.json');
                const data = await response.json();
                availableProducts = data.products;
            } catch (error) {
                console.error('Error loading products:', error);
                // Fallback to mock data
                availableProducts = [
                    { id: 1, name: 'Mahindra 575 DI Tractor', brand: 'Mahindra', image: 'images/products/mahindra-575.jpg' },
                    { id: 2, name: 'Rotavator 7 Feet', brand: 'Fieldking', image: 'images/products/rotavator-7ft.jpg' },
                    { id: 3, name: 'Seed Drill 9 Tyne', brand: 'Lemken', image: 'images/products/seed-drill-9tyne.jpg' }
                ];
            }
        }

        // Check URL parameters for pre-selected products
        function checkURLParameters() {
            const urlParams = new URLSearchParams(window.location.search);
            const productIds = urlParams.get('products');
            
            if (productIds) {
                const ids = productIds.split(',').map(id => parseInt(id));
                ids.forEach((id, index) => {
                    if (index < 3) {
                        const product = availableProducts.find(p => p.id === id);
                        if (product) {
                            addProductToComparison(product, index);
                        }
                    }
                });
            }
        }

        // Open product selector
        function openProductSelector(slotIndex) {
            currentSlot = slotIndex;
            renderProductList();
            document.getElementById('productSelectorModal').style.display = 'flex';
        }

        // Close product selector
        function closeProductSelector() {
            document.getElementById('productSelectorModal').style.display = 'none';
        }

        // Render product list in modal
        function renderProductList() {
            const productList = document.getElementById('productList');
            productList.innerHTML = availableProducts.map(product => `
                <div class="product-list-item" onclick="selectProduct(${product.id})">
                    <img src="${product.image}" alt="${product.name}" loading="lazy">
                    <div class="product-list-info">
                        <h4>${product.name}</h4>
                        <span class="product-brand">${product.brand}</span>
                    </div>
                    <button class="btn btn-small btn-primary">Select</button>
                </div>
            `).join('');
        }

        // Select product for comparison
        function selectProduct(productId) {
            const product = availableProducts.find(p => p.id === productId);
            if (product) {
                addProductToComparison(product, currentSlot);
                closeProductSelector();
            }
        }

        // Add product to comparison
        function addProductToComparison(product, slotIndex) {
            selectedProducts[slotIndex] = product;
            updateProductSelection();
            updateComparisonTable();
        }

        // Remove product from comparison
        function removeProduct(slotIndex) {
            selectedProducts[slotIndex] = null;
            updateProductSelection();
            updateComparisonTable();
        }

        // Update product selection display
        function updateProductSelection() {
            const slots = document.querySelectorAll('.selection-slot');
            
            slots.forEach((slot, index) => {
                const product = selectedProducts[index];
                
                if (product) {
                    slot.className = 'selection-slot filled';
                    slot.innerHTML = `
                        <div class="selected-product">
                            <button class="remove-product" onclick="removeProduct(${index})">
                                <i class="fas fa-times"></i>
                            </button>
                            <img src="${product.image}" alt="${product.name}" loading="lazy">
                            <div class="selected-product-info">
                                <h4>${product.name}</h4>
                                <span>${product.brand}</span>
                            </div>
                        </div>
                    `;
                    slot.onclick = null;
                } else {
                    slot.className = 'selection-slot empty';
                    slot.innerHTML = `
                        <div class="selection-placeholder">
                            <i class="fas fa-plus"></i>
                            <span>Add Product</span>
                        </div>
                    `;
                    slot.onclick = () => openProductSelector(index);
                }
            });
        }

        // Update comparison table
        function updateComparisonTable() {
            const selectedCount = selectedProducts.filter(p => p !== null).length;
            const tableContainer = document.getElementById('comparisonTableContainer');
            const noProductsMessage = document.getElementById('noProductsMessage');
            
            if (selectedCount === 0) {
                tableContainer.style.display = 'none';
                noProductsMessage.style.display = 'block';
                return;
            }
            
            tableContainer.style.display = 'block';
            noProductsMessage.style.display = 'none';
            
            const table = document.getElementById('comparisonTable');
            const products = selectedProducts.filter(p => p !== null);
            
            // Generate comparison table
            table.innerHTML = generateComparisonTable(products);
        }

        // Generate comparison table HTML
        function generateComparisonTable(products) {
            const specifications = [
                'Engine Power', 'Engine Type', 'Displacement', 'Gearbox', 
                'Steering', 'Fuel Tank', 'Weight'
            ];
            
            let tableHTML = `
                <table class="comparison-table-element">
                    <thead>
                        <tr>
                            <th class="spec-header">Specification</th>
                            ${products.map(product => `
                                <th class="product-header">
                                    <div class="product-header-content">
                                        <img src="${product.image}" alt="${product.name}" loading="lazy">
                                        <div class="product-header-info">
                                            <h4>${product.name}</h4>
                                            <span>${product.brand}</span>
                                        </div>
                                    </div>
                                </th>
                            `).join('')}
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            specifications.forEach(spec => {
                tableHTML += `
                    <tr>
                        <td class="spec-name">${spec}</td>
                        ${products.map(product => `
                            <td class="spec-value">
                                ${product.specifications && product.specifications[spec] || 'N/A'}
                            </td>
                        `).join('')}
                    </tr>
                `;
            });
            
            tableHTML += `
                    </tbody>
                </table>
                <div class="comparison-actions-row">
                    ${products.map(product => `
                        <div class="product-actions">
                            <a href="product-detail.html?id=${product.id}" class="btn btn-primary btn-small">
                                View Details
                            </a>
                            <button class="btn btn-secondary btn-small" onclick="requestQuote(${product.id})">
                                Get Quote
                            </button>
                        </div>
                    `).join('')}
                </div>
            `;
            
            return tableHTML;
        }

        // Clear all comparisons
        function clearComparison() {
            selectedProducts = [null, null, null];
            updateProductSelection();
            updateComparisonTable();
        }

        // Request quote
        function requestQuote(productId) {
            localStorage.setItem('quoteProductId', productId);
            window.location.href = 'contact.html?action=quote&product=' + productId;
        }

        // Product search in modal
        document.getElementById('productSearchInput').addEventListener('input', function() {
            const query = this.value.toLowerCase();
            const filteredProducts = availableProducts.filter(product => 
                product.name.toLowerCase().includes(query) || 
                product.brand.toLowerCase().includes(query)
            );
            
            const productList = document.getElementById('productList');
            productList.innerHTML = filteredProducts.map(product => `
                <div class="product-list-item" onclick="selectProduct(${product.id})">
                    <img src="${product.image}" alt="${product.name}" loading="lazy">
                    <div class="product-list-info">
                        <h4>${product.name}</h4>
                        <span class="product-brand">${product.brand}</span>
                    </div>
                    <button class="btn btn-small btn-primary">Select</button>
                </div>
            `).join('');
        });

        // Close modal when clicking outside
        document.getElementById('productSelectorModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeProductSelector();
            }
        });
    </script>
</body>
</html>
