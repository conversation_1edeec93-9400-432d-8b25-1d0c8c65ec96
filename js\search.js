// Advanced search and filtering functionality for a.agrotech

// Search configuration
const searchConfig = {
    minQueryLength: 2,
    debounceDelay: 300,
    maxSuggestions: 8,
    fuzzyThreshold: 0.6
};

// Product database - will be loaded from JSON
let productDatabase = [];

// Load product database from JSON file
async function loadProductDatabase() {
    try {
        const response = await fetch('data/products.json');
        const data = await response.json();
        productDatabase = data.products;
        console.log('Product database loaded:', productDatabase.length, 'products');
    } catch (error) {
        console.error('Error loading product database:', error);
        // Fallback to empty array
        productDatabase = [];
    }
}

// Initialize advanced search
function initAdvancedSearch() {
    setupSearchInput();
    setupFilters();
    setupAutoComplete();
    setupFuzzySearch();
}

// Setup search input with debouncing
function setupSearchInput() {
    const searchInputs = document.querySelectorAll('.search-input, #productSearch');
    
    searchInputs.forEach(input => {
        let searchTimeout;
        
        input.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            if (query.length >= searchConfig.minQueryLength) {
                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, searchConfig.debounceDelay);
            } else {
                clearSearchResults();
            }
        });
        
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const query = this.value.trim();
                if (query.length >= searchConfig.minQueryLength) {
                    redirectToSearchResults(query);
                }
            }
        });
    });
}

// Perform search with fuzzy matching
function performSearch(query) {
    const results = fuzzySearch(query, productDatabase);
    displaySearchSuggestions(results, query);
}

// Fuzzy search implementation
function fuzzySearch(query, database) {
    const queryLower = query.toLowerCase();
    const results = [];
    
    database.forEach(product => {
        let score = 0;
        const searchableText = [
            product.name,
            product.brand,
            product.category,
            ...product.keywords,
            ...product.crop
        ].join(' ').toLowerCase();
        
        // Exact match gets highest score
        if (searchableText.includes(queryLower)) {
            score += 1.0;
        }
        
        // Fuzzy matching for typos
        const fuzzyScore = calculateFuzzyScore(queryLower, searchableText);
        score += fuzzyScore;
        
        // Keyword matching
        const keywordScore = calculateKeywordScore(queryLower, product.keywords);
        score += keywordScore;
        
        if (score >= searchConfig.fuzzyThreshold) {
            results.push({
                ...product,
                searchScore: score
            });
        }
    });
    
    // Sort by relevance score
    return results.sort((a, b) => b.searchScore - a.searchScore)
                  .slice(0, searchConfig.maxSuggestions);
}

// Calculate fuzzy matching score
function calculateFuzzyScore(query, text) {
    const words = query.split(' ');
    let totalScore = 0;
    
    words.forEach(word => {
        if (word.length < 3) return;
        
        const regex = new RegExp(word.split('').join('.*'), 'i');
        if (regex.test(text)) {
            totalScore += 0.3;
        }
        
        // Levenshtein distance for typo tolerance
        const textWords = text.split(' ');
        textWords.forEach(textWord => {
            const distance = levenshteinDistance(word, textWord);
            if (distance <= 2 && word.length > 3) {
                totalScore += 0.2;
            }
        });
    });
    
    return totalScore;
}

// Calculate keyword matching score
function calculateKeywordScore(query, keywords) {
    let score = 0;
    const queryWords = query.split(' ');
    
    queryWords.forEach(queryWord => {
        keywords.forEach(keyword => {
            if (keyword.toLowerCase().includes(queryWord)) {
                score += 0.5;
            }
        });
    });
    
    return score;
}

// Levenshtein distance for typo tolerance
function levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
        matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
        matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
        for (let j = 1; j <= str1.length; j++) {
            if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j] + 1
                );
            }
        }
    }
    
    return matrix[str2.length][str1.length];
}

// Display search suggestions with enhanced UI
function displaySearchSuggestions(results, query) {
    const suggestionsContainer = document.getElementById('searchSuggestions');
    if (!suggestionsContainer) return;
    
    if (results.length === 0) {
        suggestionsContainer.innerHTML = `
            <div class="suggestion-item no-results">
                <i class="fas fa-search"></i>
                <div>
                    <div class="suggestion-title">No results found for "${query}"</div>
                    <div class="suggestion-subtitle">Try different keywords or check spelling</div>
                </div>
            </div>
        `;
    } else {
        suggestionsContainer.innerHTML = results.map(product => `
            <div class="suggestion-item" onclick="selectProduct(${product.id})">
                <i class="fas fa-${getCategoryIcon(product.category)}"></i>
                <div class="suggestion-content">
                    <div class="suggestion-title">${highlightMatch(product.name, query)}</div>
                    <div class="suggestion-subtitle">${product.brand} • ${product.category}</div>
                </div>
                <div class="suggestion-score">${Math.round(product.searchScore * 100)}% match</div>
            </div>
        `).join('');
        
        // Add "View all results" option
        suggestionsContainer.innerHTML += `
            <div class="suggestion-item view-all" onclick="redirectToSearchResults('${query}')">
                <i class="fas fa-arrow-right"></i>
                <div class="suggestion-content">
                    <div class="suggestion-title">View all results for "${query}"</div>
                </div>
            </div>
        `;
    }
    
    suggestionsContainer.classList.add('active');
}

// Get category icon
function getCategoryIcon(category) {
    const icons = {
        'processing': 'cogs',
        'protection': 'spray-can',
        'maintenance': 'tools'
    };
    return icons[category] || 'cog';
}

// Highlight matching text
function highlightMatch(text, query) {
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
}

// Select product from suggestions
function selectProduct(productId) {
    window.location.href = `product-detail.html?id=${productId}`;
}

// Redirect to search results page
function redirectToSearchResults(query) {
    window.location.href = `products.html?search=${encodeURIComponent(query)}`;
}

// Clear search results
function clearSearchResults() {
    const suggestionsContainer = document.getElementById('searchSuggestions');
    if (suggestionsContainer) {
        suggestionsContainer.classList.remove('active');
        suggestionsContainer.innerHTML = '';
    }
}

// Setup filters for product listing page
function setupFilters() {
    const filterElements = document.querySelectorAll('.filter-checkbox, .filter-select');
    
    filterElements.forEach(element => {
        element.addEventListener('change', function() {
            applyFilters();
        });
    });
}

// Apply filters to product listing
function applyFilters() {
    const activeFilters = getActiveFilters();
    const filteredProducts = filterProducts(productDatabase, activeFilters);
    
    // Update product grid
    const productGrid = document.querySelector('.product-grid');
    if (productGrid) {
        renderFilteredProducts(filteredProducts, productGrid);
    }
    
    // Update filter counts
    updateFilterCounts(activeFilters);
}

// Get active filters
function getActiveFilters() {
    const filters = {
        categories: [],
        brands: [],
        crops: [],
        priceRange: null,
        powerRange: null
    };
    
    // Category filters
    document.querySelectorAll('.filter-category:checked').forEach(checkbox => {
        filters.categories.push(checkbox.value);
    });
    
    // Brand filters
    document.querySelectorAll('.filter-brand:checked').forEach(checkbox => {
        filters.brands.push(checkbox.value);
    });
    
    // Crop filters
    document.querySelectorAll('.filter-crop:checked').forEach(checkbox => {
        filters.crops.push(checkbox.value);
    });
    
    return filters;
}

// Filter products based on active filters
function filterProducts(products, filters) {
    return products.filter(product => {
        // Category filter
        if (filters.categories.length > 0 && !filters.categories.includes(product.category)) {
            return false;
        }
        
        // Brand filter
        if (filters.brands.length > 0 && !filters.brands.includes(product.brand.toLowerCase())) {
            return false;
        }
        
        // Crop filter
        if (filters.crops.length > 0) {
            const hasMatchingCrop = filters.crops.some(crop => product.crop.includes(crop));
            if (!hasMatchingCrop) {
                return false;
            }
        }
        
        return true;
    });
}

// Render filtered products
function renderFilteredProducts(products, container) {
    if (products.length === 0) {
        container.innerHTML = `
            <div class="no-products">
                <i class="fas fa-search"></i>
                <h3>No products found</h3>
                <p>Try adjusting your filters or search terms</p>
                <button class="btn btn-primary" onclick="clearAllFilters()">Clear Filters</button>
            </div>
        `;
        return;
    }
    
    // Use the existing renderProducts function from main.js
    if (typeof renderProducts === 'function') {
        renderProducts(products, container);
    }
}

// Clear all filters
function clearAllFilters() {
    document.querySelectorAll('.filter-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    
    document.querySelectorAll('.filter-select').forEach(select => {
        select.selectedIndex = 0;
    });
    
    applyFilters();
}

// Update filter counts
function updateFilterCounts(filters) {
    const totalProducts = productDatabase.length;
    const filteredProducts = filterProducts(productDatabase, filters);
    
    const resultCount = document.querySelector('.result-count');
    if (resultCount) {
        resultCount.textContent = `Showing ${filteredProducts.length} of ${totalProducts} products`;
    }
}

// Setup autocomplete functionality
function setupAutoComplete() {
    const searchInputs = document.querySelectorAll('.search-input');
    
    searchInputs.forEach(input => {
        input.setAttribute('autocomplete', 'off');
        input.setAttribute('spellcheck', 'false');
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    loadProductDatabase().then(() => {
        initAdvancedSearch();
    });
});

// Export functions for use in other scripts
window.searchFunctions = {
    performSearch,
    fuzzySearch,
    selectProduct,
    redirectToSearchResults,
    clearSearchResults,
    applyFilters,
    clearAllFilters
};
