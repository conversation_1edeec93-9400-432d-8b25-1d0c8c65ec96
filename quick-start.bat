@echo off
title Quick Start - Agrotech Website
color 0B
echo.
echo ==========================================
echo     QUICK START - AGROTECH WEBSITE
echo ==========================================
echo.

:: Change to script directory
cd /d "%~dp0"

:: Try to start with Python (most common)
echo Attempting to start server...
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✓ Starting with Python on http://localhost:8080
    echo.
    echo Opening browser in 3 seconds...
    timeout /t 3 /nobreak >nul
    start http://localhost:8080
    echo.
    echo ==========================================
    echo  🌐 AGROTECH WEBSITE IS NOW RUNNING!
    echo  📍 URL: http://localhost:8080
    echo  🛑 Press Ctrl+C to stop the server
    echo ==========================================
    echo.
    python -m http.server 8080
) else (
    echo.
    echo ❌ Python not found!
    echo.
    echo Please install Python from: https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation.
    echo.
    echo Or run 'start-server.bat' for more server options.
    echo.
    pause
)
