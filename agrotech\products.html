<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#2e7d32">
    <meta name="msapplication-navbutton-color" content="#2e7d32">
    <meta name="apple-mobile-web-app-title" content="a.agrotech">
    <title>Agricultural Equipment Products - a.agrotech | Find the Right Farming Equipment</title>
    <meta name="description" content="Browse comprehensive collection of agricultural equipment. Compare specifications, prices, and connect with verified dealers across India.">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.html" class="logo">
                        <i class="fas fa-seedling"></i>
                        <span>a.agrotech</span>
                    </a>
                </div>
                
                <!-- Mobile Menu Toggle -->
                <button class="mobile-menu-toggle" aria-label="Toggle menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                
                <!-- Navigation Menu -->
                <div class="nav-menu">
                    <ul class="nav-links">
                        <li><a href="index.html">Home</a></li>
                        <li class="dropdown">
                            <a href="products.html" class="active">Products <i class="fas fa-chevron-down"></i></a>
                            <div class="dropdown-menu">
                                <div class="dropdown-section">
                                    <h4>Shop by Category</h4>
                                    <a href="products.html?category=processing">Processing Equipment</a>
                                    <a href="products.html?category=protection">Crop Protection</a>
                                    <a href="products.html?category=maintenance">Maintenance Equipment</a>
                                </div>
                                <div class="dropdown-section">
                                    <h4>Shop by Brand</h4>
                                    <a href="products.html?brand=agrotech">AgroTech</a>
                                </div>
                                <div class="dropdown-section">
                                    <h4>Shop by Crop</h4>
                                    <a href="products.html?crop=arecanut">Arecanut Solutions</a>
                                    <a href="products.html?crop=coconut">Coconut Solutions</a>
                                    <a href="products.html?crop=vegetables">Vegetable Solutions</a>
                                </div>
                            </div>
                        </li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="photo-gallery.html">Gallery</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                    
                    <!-- Search Bar -->
                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="Search equipment..." id="searchInput">
                        <button class="search-btn" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                        <div class="search-suggestions" id="searchSuggestions"></div>
                    </div>
                    
                    <!-- Contact Info -->
                    <div class="header-contact">
                        <a href="tel:1800-123-4567" class="toll-free">
                            <i class="fas fa-phone"></i>
                            <span>1800-123-4567</span>
                        </a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content">
                <h1 id="pageTitle">Agricultural Equipment</h1>
                <p id="pageDescription">Find the perfect equipment for your farming needs</p>
                <nav class="breadcrumb">
                    <a href="index.html">Home</a>
                    <span>/</span>
                    <span id="breadcrumbCurrent">Products</span>
                </nav>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section class="products-section">
        <div class="container">
            <div class="products-layout">
                <!-- Filters Sidebar -->
                <aside class="filters-sidebar">
                    <div class="filters-header">
                        <h3>Filter Products</h3>
                        <button class="clear-filters" onclick="clearAllFilters()">
                            <i class="fas fa-times"></i>
                            Clear All
                        </button>
                    </div>
                    
                    <!-- Search Filter -->
                    <div class="filter-section">
                        <h4>Search</h4>
                        <div class="search-filter">
                            <input type="text" id="productSearch" class="form-input" placeholder="Search products...">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                    
                    <!-- Category Filter -->
                    <div class="filter-section">
                        <h4>Category</h4>
                        <div class="filter-options">
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-category" value="processing">
                                <span class="checkmark"></span>
                                <span class="filter-label">Processing Equipment</span>
                                <span class="filter-count">(1)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-category" value="protection">
                                <span class="checkmark"></span>
                                <span class="filter-label">Crop Protection</span>
                                <span class="filter-count">(2)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-category" value="maintenance">
                                <span class="checkmark"></span>
                                <span class="filter-label">Maintenance Equipment</span>
                                <span class="filter-count">(1)</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Brand Filter -->
                    <div class="filter-section">
                        <h4>Brand</h4>
                        <div class="filter-options">
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-brand" value="agrotech">
                                <span class="checkmark"></span>
                                <span class="filter-label">AgroTech</span>
                                <span class="filter-count">(4)</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Crop Filter -->
                    <div class="filter-section">
                        <h4>Suitable for Crop</h4>
                        <div class="filter-options">
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-crop" value="arecanut">
                                <span class="checkmark"></span>
                                <span class="filter-label">Arecanut</span>
                                <span class="filter-count">(2)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-crop" value="betel nut">
                                <span class="checkmark"></span>
                                <span class="filter-label">Betel Nut</span>
                                <span class="filter-count">(1)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-crop" value="coconut">
                                <span class="checkmark"></span>
                                <span class="filter-label">Coconut</span>
                                <span class="filter-count">(1)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-crop" value="vegetables">
                                <span class="checkmark"></span>
                                <span class="filter-label">Vegetables</span>
                                <span class="filter-count">(1)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-crop" value="fruits">
                                <span class="checkmark"></span>
                                <span class="filter-label">Fruits</span>
                                <span class="filter-count">(1)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-crop" value="grass">
                                <span class="checkmark"></span>
                                <span class="filter-label">Grass/Weeds</span>
                                <span class="filter-count">(1)</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Power Range Filter -->
                    <div class="filter-section">
                        <h4>Power Required (HP)</h4>
                        <div class="filter-options">
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-power" value="20-35">
                                <span class="checkmark"></span>
                                <span class="filter-label">20-35 HP</span>
                                <span class="filter-count">(25)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-power" value="35-50">
                                <span class="checkmark"></span>
                                <span class="filter-label">35-50 HP</span>
                                <span class="filter-count">(35)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-power" value="50-75">
                                <span class="checkmark"></span>
                                <span class="filter-label">50-75 HP</span>
                                <span class="filter-count">(28)</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" class="filter-checkbox filter-power" value="75+">
                                <span class="checkmark"></span>
                                <span class="filter-label">75+ HP</span>
                                <span class="filter-count">(15)</span>
                            </label>
                        </div>
                    </div>
                </aside>
                
                <!-- Products Content -->
                <main class="products-content">
                    <!-- Active Filters -->
                    <div class="active-filters" id="activeFilters">
                        <!-- Active filters will be displayed here -->
                    </div>
                    
                    <!-- Products Header -->
                    <div class="products-header">
                        <div class="results-info">
                            <span class="result-count" id="resultCount">Showing 130 products</span>
                        </div>
                        <div class="sort-options">
                            <label for="sortSelect">Sort by:</label>
                            <select id="sortSelect" class="form-select">
                                <option value="relevance">Relevance</option>
                                <option value="name-asc">Name (A-Z)</option>
                                <option value="name-desc">Name (Z-A)</option>
                                <option value="brand-asc">Brand (A-Z)</option>
                                <option value="popular">Most Popular</option>
                            </select>
                        </div>
                        <div class="view-options">
                            <button class="view-btn active" data-view="grid" title="Grid View">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="view-btn" data-view="list" title="List View">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Products Grid -->
                    <div class="products-grid" id="productsGrid">
                        <!-- Products will be loaded dynamically -->
                    </div>
                    
                    <!-- Load More Button -->
                    <div class="load-more-container">
                        <button class="btn btn-outline" id="loadMoreBtn">
                            <i class="fas fa-plus"></i>
                            Load More Products
                        </button>
                    </div>
                </main>
            </div>
        </div>
    </section>

    <!-- Comparison Bar -->
    <div class="comparison-bar" id="comparisonBar">
        <div class="container">
            <div class="comparison-content">
                <div class="comparison-info">
                    <span class="comparison-count">0</span> products selected for comparison
                </div>
                <div class="comparison-actions">
                    <button class="btn btn-primary" id="compareBtn" disabled>
                        <i class="fas fa-balance-scale"></i>
                        Compare Products
                    </button>
                    <button class="btn btn-outline" id="clearComparisonBtn">
                        <i class="fas fa-times"></i>
                        Clear
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <a href="index.html" class="logo">
                            <i class="fas fa-seedling"></i>
                            <span>a.agrotech</span>
                        </a>
                        <p>Empowering Indian farmers with the right agricultural equipment and trusted dealer connections.</p>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="photo-gallery.html">Gallery</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Categories</h4>
                    <ul>
                        <li><a href="products.html?category=processing">Processing Equipment</a></li>
                        <li><a href="products.html?category=protection">Crop Protection</a></li>
                        <li><a href="products.html?category=maintenance">Maintenance Equipment</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-phone"></i> 1800-123-4567 (Toll Free)</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> New Delhi, India</p>
                    </div>
                    <div class="social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="#" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 a.agrotech. All rights reserved. | <a href="#">Privacy Policy</a> | <a href="#">Terms of Service</a></p>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Float Button -->
    <a href="https://wa.me/************" class="whatsapp-float" target="_blank" aria-label="Chat on WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/search.js"></script>
    <script>
        // Initialize products page
        document.addEventListener('DOMContentLoaded', function() {
            initProductsPage();
            loadProducts();
            setupFilters();
            setupSorting();
            setupViewToggle();
            setupComparison();
            handleURLParameters();
        });
        
        // Products page initialization
        function initProductsPage() {
            console.log('Products page initialized');
        }
        
        // Load products from JSON file
        async function loadProducts() {
            try {
                const response = await fetch('data/products.json');
                const data = await response.json();
                allProducts = data.products;
                filteredProducts = [...allProducts];
                renderProducts(filteredProducts, document.getElementById('productsGrid'));
                updateResultCount(filteredProducts.length);
                updateFilterCounts();
            } catch (error) {
                console.error('Error loading products:', error);
                // Fallback to empty state
                document.getElementById('productsGrid').innerHTML = `
                    <div class="no-products">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>Unable to load products</h3>
                        <p>Please check your connection and try again</p>
                        <button class="btn btn-primary" onclick="loadProducts()">Retry</button>
                    </div>
                `;
            }
        }

        // Global variables for product management
        let allProducts = [];
        let filteredProducts = [];
        
        // Setup filters
        function setupFilters() {
            const filterCheckboxes = document.querySelectorAll('.filter-checkbox');
            filterCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', applyFilters);
            });
            
            const searchFilter = document.getElementById('productSearch');
            if (searchFilter) {
                searchFilter.addEventListener('input', debounce(applyFilters, 300));
            }
        }
        
        // Setup sorting
        function setupSorting() {
            const sortSelect = document.getElementById('sortSelect');
            if (sortSelect) {
                sortSelect.addEventListener('change', applySorting);
            }
        }
        
        // Setup view toggle
        function setupViewToggle() {
            const viewButtons = document.querySelectorAll('.view-btn');
            viewButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    viewButtons.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    
                    const view = this.dataset.view;
                    const productsGrid = document.getElementById('productsGrid');
                    productsGrid.className = view === 'list' ? 'products-list' : 'products-grid';
                });
            });
        }
        
        // Setup comparison
        function setupComparison() {
            // Comparison functionality will be implemented
        }
        
        // Handle URL parameters
        function handleURLParameters() {
            const urlParams = new URLSearchParams(window.location.search);
            
            // Handle category parameter
            const category = urlParams.get('category');
            if (category) {
                const categoryCheckbox = document.querySelector(`.filter-category[value="${category}"]`);
                if (categoryCheckbox) {
                    categoryCheckbox.checked = true;
                    updatePageTitle(category);
                }
            }
            
            // Handle brand parameter
            const brand = urlParams.get('brand');
            if (brand) {
                const brandCheckbox = document.querySelector(`.filter-brand[value="${brand}"]`);
                if (brandCheckbox) {
                    brandCheckbox.checked = true;
                    updatePageTitle(null, brand);
                }
            }
            
            // Handle crop parameter
            const crop = urlParams.get('crop');
            if (crop) {
                const cropCheckbox = document.querySelector(`.filter-crop[value="${crop}"]`);
                if (cropCheckbox) {
                    cropCheckbox.checked = true;
                    updatePageTitle(null, null, crop);
                }
            }
            
            // Apply filters if any parameters were set
            if (category || brand || crop) {
                applyFilters();
            }
        }
        
        // Update page title based on filters
        function updatePageTitle(category, brand, crop) {
            const pageTitle = document.getElementById('pageTitle');
            const pageDescription = document.getElementById('pageDescription');
            const breadcrumbCurrent = document.getElementById('breadcrumbCurrent');
            
            if (category) {
                const categoryNames = {
                    'tillage': 'Tillage Equipment',
                    'sowing': 'Sowing & Planting Equipment',
                    'protection': 'Crop Protection Equipment',
                    'harvesting': 'Harvesting Equipment'
                };
                pageTitle.textContent = categoryNames[category];
                pageDescription.textContent = `Find the best ${categoryNames[category].toLowerCase()} for your farming needs`;
                breadcrumbCurrent.textContent = categoryNames[category];
            } else if (brand) {
                pageTitle.textContent = `${brand} Equipment`;
                pageDescription.textContent = `Explore ${brand} agricultural equipment collection`;
                breadcrumbCurrent.textContent = `${brand} Products`;
            } else if (crop) {
                const cropNames = {
                    'wheat': 'Wheat Farming Equipment',
                    'paddy': 'Paddy/Rice Farming Equipment',
                    'sugarcane': 'Sugarcane Farming Equipment',
                    'cotton': 'Cotton Farming Equipment'
                };
                pageTitle.textContent = cropNames[crop];
                pageDescription.textContent = `Complete equipment solutions for ${crop} farming`;
                breadcrumbCurrent.textContent = cropNames[crop];
            }
        }
        
        // Apply filters function
        function applyFilters() {
            const activeFilters = getActiveFilters();
            filteredProducts = filterProducts(allProducts, activeFilters);

            // Apply current sorting
            const sortValue = document.getElementById('sortSelect').value;
            filteredProducts = sortProducts(filteredProducts, sortValue);

            // Render filtered products
            renderProducts(filteredProducts, document.getElementById('productsGrid'));
            updateResultCount(filteredProducts.length);
            updateActiveFiltersDisplay(activeFilters);
        }

        // Get active filters
        function getActiveFilters() {
            const filters = {
                search: document.getElementById('productSearch').value.toLowerCase(),
                categories: [],
                brands: [],
                crops: [],
                power: []
            };

            // Get checked category filters
            document.querySelectorAll('.filter-category:checked').forEach(checkbox => {
                filters.categories.push(checkbox.value);
            });

            // Get checked brand filters
            document.querySelectorAll('.filter-brand:checked').forEach(checkbox => {
                filters.brands.push(checkbox.value);
            });

            // Get checked crop filters
            document.querySelectorAll('.filter-crop:checked').forEach(checkbox => {
                filters.crops.push(checkbox.value);
            });

            // Get checked power filters
            document.querySelectorAll('.filter-power:checked').forEach(checkbox => {
                filters.power.push(checkbox.value);
            });

            return filters;
        }

        // Filter products based on active filters
        function filterProducts(products, filters) {
            return products.filter(product => {
                // Search filter
                if (filters.search && !product.name.toLowerCase().includes(filters.search) &&
                    !product.description.toLowerCase().includes(filters.search) &&
                    !product.keywords.some(keyword => keyword.toLowerCase().includes(filters.search))) {
                    return false;
                }

                // Category filter
                if (filters.categories.length > 0 && !filters.categories.includes(product.category)) {
                    return false;
                }

                // Brand filter
                if (filters.brands.length > 0 && !filters.brands.includes(product.brand.toLowerCase())) {
                    return false;
                }

                // Crop filter
                if (filters.crops.length > 0 && !product.crop.some(crop => filters.crops.includes(crop))) {
                    return false;
                }

                return true;
            });
        }

        // Sort products
        function sortProducts(products, sortBy) {
            const sortedProducts = [...products];

            switch (sortBy) {
                case 'name-asc':
                    return sortedProducts.sort((a, b) => a.name.localeCompare(b.name));
                case 'name-desc':
                    return sortedProducts.sort((a, b) => b.name.localeCompare(a.name));
                case 'brand-asc':
                    return sortedProducts.sort((a, b) => a.brand.localeCompare(b.brand));
                case 'popular':
                    return sortedProducts.sort((a, b) => (b.rating || 0) - (a.rating || 0));
                default:
                    return sortedProducts;
            }
        }

        // Apply sorting function
        function applySorting() {
            const sortValue = document.getElementById('sortSelect').value;
            filteredProducts = sortProducts(filteredProducts, sortValue);
            renderProducts(filteredProducts, document.getElementById('productsGrid'));
        }

        // Update result count
        function updateResultCount(count) {
            const resultCountElement = document.getElementById('resultCount');
            if (resultCountElement) {
                resultCountElement.textContent = `Showing ${count} product${count !== 1 ? 's' : ''}`;
            }
        }

        // Update active filters display
        function updateActiveFiltersDisplay(filters) {
            const activeFiltersContainer = document.getElementById('activeFilters');
            const activeFilterTags = [];

            // Add search filter
            if (filters.search) {
                activeFilterTags.push(`Search: "${filters.search}"`);
            }

            // Add category filters
            filters.categories.forEach(category => {
                const categoryNames = {
                    'processing': 'Processing Equipment',
                    'protection': 'Crop Protection',
                    'maintenance': 'Maintenance Equipment'
                };
                activeFilterTags.push(categoryNames[category] || category);
            });

            // Add brand filters
            filters.brands.forEach(brand => {
                activeFilterTags.push(`Brand: ${brand}`);
            });

            // Add crop filters
            filters.crops.forEach(crop => {
                activeFilterTags.push(`Crop: ${crop}`);
            });

            if (activeFilterTags.length > 0) {
                activeFiltersContainer.innerHTML = `
                    <div class="active-filters-content">
                        <span class="active-filters-label">Active Filters:</span>
                        ${activeFilterTags.map(tag => `
                            <span class="filter-tag">
                                ${tag}
                                <button onclick="removeFilter('${tag}')" class="remove-filter">×</button>
                            </span>
                        `).join('')}
                        <button class="clear-all-filters" onclick="clearAllFilters()">Clear All</button>
                    </div>
                `;
                activeFiltersContainer.style.display = 'block';
            } else {
                activeFiltersContainer.style.display = 'none';
            }
        }

        // Update filter counts
        function updateFilterCounts() {
            // This would update the counts next to each filter option
            // For now, we'll keep the static counts
        }

        // Clear all filters
        function clearAllFilters() {
            // Clear search
            document.getElementById('productSearch').value = '';

            // Uncheck all filter checkboxes
            document.querySelectorAll('.filter-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });

            // Reset products
            filteredProducts = [...allProducts];
            renderProducts(filteredProducts, document.getElementById('productsGrid'));
            updateResultCount(filteredProducts.length);
            document.getElementById('activeFilters').style.display = 'none';
        }
    </script>
</body>
</html>
