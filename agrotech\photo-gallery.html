<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Photo Gallery - a.agrotech | Agricultural Equipment in Action</title>
    <meta name="description" content="View our comprehensive photo gallery showcasing agricultural equipment in real farming conditions across India.">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.html" class="logo">
                        <i class="fas fa-seedling"></i>
                        <span>a.agrotech</span>
                    </a>
                </div>
                
                <!-- Mobile Menu Toggle -->
                <button class="mobile-menu-toggle" aria-label="Toggle menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                
                <!-- Navigation Menu -->
                <div class="nav-menu">
                    <ul class="nav-links">
                        <li><a href="index.html">Home</a></li>
                        <li class="dropdown">
                            <a href="products.html">Products <i class="fas fa-chevron-down"></i></a>
                            <div class="dropdown-menu">
                                <div class="dropdown-section">
                                    <h4>Shop by Category</h4>
                                    <a href="products.html?category=tillage">Tillage Equipment</a>
                                    <a href="products.html?category=sowing">Sowing & Planting</a>
                                    <a href="products.html?category=protection">Crop Protection</a>
                                    <a href="products.html?category=harvesting">Harvesting</a>
                                </div>
                                <div class="dropdown-section">
                                    <h4>Shop by Brand</h4>
                                    <a href="products.html?brand=mahindra">Mahindra</a>
                                    <a href="products.html?brand=sonalika">Sonalika</a>
                                    <a href="products.html?brand=johndeere">John Deere</a>
                                </div>
                                <div class="dropdown-section">
                                    <h4>Shop by Crop</h4>
                                    <a href="products.html?crop=paddy">Paddy Solutions</a>
                                    <a href="products.html?crop=wheat">Wheat Solutions</a>
                                    <a href="products.html?crop=sugarcane">Sugarcane Solutions</a>
                                </div>
                            </div>
                        </li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="photo-gallery.html" class="active">Gallery</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                    
                    <!-- Search Bar -->
                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="Search equipment..." id="searchInput">
                        <button class="search-btn" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                        <div class="search-suggestions" id="searchSuggestions"></div>
                    </div>
                    
                    <!-- Contact Info -->
                    <div class="header-contact">
                        <a href="tel:1800-123-4567" class="toll-free">
                            <i class="fas fa-phone"></i>
                            <span>1800-123-4567</span>
                        </a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content">
                <h1>Photo Gallery</h1>
                <p>See agricultural equipment in action across Indian farms</p>
                <nav class="breadcrumb">
                    <a href="index.html">Home</a>
                    <span>/</span>
                    <span>Gallery</span>
                </nav>
            </div>
        </div>
    </section>

    <!-- Gallery Navigation -->
    <section class="gallery-nav">
        <div class="container">
            <div class="gallery-filters">
                <button class="filter-btn active" data-filter="all">All Photos</button>
                <button class="filter-btn" data-filter="tractors">Tractors</button>
                <button class="filter-btn" data-filter="tillage">Tillage Equipment</button>
                <button class="filter-btn" data-filter="sowing">Sowing Equipment</button>
                <button class="filter-btn" data-filter="harvesting">Harvesting</button>
                <button class="filter-btn" data-filter="farmers">Farmers at Work</button>
            </div>
        </div>
    </section>

    <!-- Photo Gallery -->
    <section class="photo-gallery-section">
        <div class="container">
            <div class="gallery-grid" id="galleryGrid">
                <!-- Gallery items will be loaded dynamically -->
            </div>
            
            <!-- Load More Button -->
            <div class="load-more-container">
                <button class="btn btn-outline" id="loadMorePhotos">
                    <i class="fas fa-plus"></i>
                    Load More Photos
                </button>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="gallery-stats">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-camera"></i>
                    </div>
                    <div class="stat-number">500+</div>
                    <div class="stat-label">Photos</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-tractor"></i>
                    </div>
                    <div class="stat-number">150+</div>
                    <div class="stat-label">Equipment Types</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="stat-number">25+</div>
                    <div class="stat-label">States Covered</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-number">1000+</div>
                    <div class="stat-label">Happy Farmers</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Video Gallery Link -->
    <section class="video-gallery-link">
        <div class="container">
            <div class="video-link-card">
                <div class="video-link-content">
                    <div class="video-link-icon">
                        <i class="fas fa-play-circle"></i>
                    </div>
                    <div class="video-link-text">
                        <h3>Watch Equipment in Action</h3>
                        <p>See detailed video demonstrations of agricultural equipment working in real farm conditions</p>
                    </div>
                </div>
                <a href="video-gallery.html" class="btn btn-primary">
                    <i class="fas fa-video"></i>
                    View Video Gallery
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <a href="index.html" class="logo">
                            <i class="fas fa-seedling"></i>
                            <span>a.agrotech</span>
                        </a>
                        <p>Empowering Indian farmers with the right agricultural equipment and trusted dealer connections.</p>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="photo-gallery.html">Gallery</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Categories</h4>
                    <ul>
                        <li><a href="products.html?category=tillage">Tillage Equipment</a></li>
                        <li><a href="products.html?category=sowing">Sowing & Planting</a></li>
                        <li><a href="products.html?category=protection">Crop Protection</a></li>
                        <li><a href="products.html?category=harvesting">Harvesting</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-phone"></i> 1800-123-4567 (Toll Free)</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> New Delhi, India</p>
                    </div>
                    <div class="social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="#" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 a.agrotech. All rights reserved. | <a href="#">Privacy Policy</a> | <a href="#">Terms of Service</a></p>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Float Button -->
    <a href="https://wa.me/************" class="whatsapp-float" target="_blank" aria-label="Chat on WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Lightbox Modal -->
    <div class="lightbox-modal" id="lightboxModal">
        <div class="lightbox-content">
            <button class="lightbox-close" onclick="closeLightbox()">&times;</button>
            <button class="lightbox-prev" onclick="prevImage()">
                <i class="fas fa-chevron-left"></i>
            </button>
            <button class="lightbox-next" onclick="nextImage()">
                <i class="fas fa-chevron-right"></i>
            </button>
            <img id="lightboxImage" src="" alt="">
            <div class="lightbox-info">
                <h3 id="lightboxTitle"></h3>
                <p id="lightboxDescription"></p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/search.js"></script>
    <script>
        // Gallery data
        const galleryData = [
            {
                id: 1,
                src: 'images/gallery/tractor-field-1.jpg',
                thumb: 'images/gallery/thumbs/tractor-field-1.jpg',
                title: 'Mahindra Tractor in Wheat Field',
                description: 'Mahindra 575 DI working efficiently in a wheat field in Punjab',
                category: 'tractors',
                location: 'Punjab'
            },
            {
                id: 2,
                src: 'images/gallery/rotavator-action-1.jpg',
                thumb: 'images/gallery/thumbs/rotavator-action-1.jpg',
                title: 'Rotavator Soil Preparation',
                description: 'Heavy-duty rotavator preparing soil for sowing season',
                category: 'tillage',
                location: 'Haryana'
            },
            {
                id: 3,
                src: 'images/gallery/seed-drill-sowing-1.jpg',
                thumb: 'images/gallery/thumbs/seed-drill-sowing-1.jpg',
                title: 'Precision Seed Drilling',
                description: 'Modern seed drill ensuring precise seed placement',
                category: 'sowing',
                location: 'Uttar Pradesh'
            },
            {
                id: 4,
                src: 'images/gallery/combine-harvester-1.jpg',
                thumb: 'images/gallery/thumbs/combine-harvester-1.jpg',
                title: 'Combine Harvester in Action',
                description: 'Efficient wheat harvesting with modern combine harvester',
                category: 'harvesting',
                location: 'Madhya Pradesh'
            },
            {
                id: 5,
                src: 'images/gallery/farmer-tractor-1.jpg',
                thumb: 'images/gallery/thumbs/farmer-tractor-1.jpg',
                title: 'Farmer with New Tractor',
                description: 'Happy farmer with his newly purchased tractor',
                category: 'farmers',
                location: 'Gujarat'
            },
            {
                id: 6,
                src: 'images/gallery/sprayer-crop-protection-1.jpg',
                thumb: 'images/gallery/thumbs/sprayer-crop-protection-1.jpg',
                title: 'Crop Protection Spraying',
                description: 'Power sprayer protecting crops from pests',
                category: 'tillage',
                location: 'Maharashtra'
            },
            {
                id: 7,
                src: 'images/gallery/disc-harrow-1.jpg',
                thumb: 'images/gallery/thumbs/disc-harrow-1.jpg',
                title: 'Disc Harrow Operation',
                description: 'Disc harrow breaking soil clods after harvest',
                category: 'tillage',
                location: 'Karnataka'
            },
            {
                id: 8,
                src: 'images/gallery/farmers-training-1.jpg',
                thumb: 'images/gallery/thumbs/farmers-training-1.jpg',
                title: 'Farmer Training Session',
                description: 'Farmers learning about modern equipment operation',
                category: 'farmers',
                location: 'Tamil Nadu'
            }
        ];

        let currentImageIndex = 0;
        let filteredImages = [...galleryData];
        let displayedImages = 0;
        const imagesPerLoad = 8;

        // Initialize gallery
        document.addEventListener('DOMContentLoaded', function() {
            initGallery();
            setupFilters();
            setupLightbox();
        });

        // Initialize gallery
        function initGallery() {
            loadImages();
        }

        // Setup filter buttons
        function setupFilters() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            
            filterButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    // Remove active class from all buttons
                    filterButtons.forEach(b => b.classList.remove('active'));
                    
                    // Add active class to clicked button
                    this.classList.add('active');
                    
                    // Filter images
                    const filter = this.dataset.filter;
                    filterImages(filter);
                });
            });
        }

        // Filter images
        function filterImages(category) {
            if (category === 'all') {
                filteredImages = [...galleryData];
            } else {
                filteredImages = galleryData.filter(img => img.category === category);
            }
            
            displayedImages = 0;
            document.getElementById('galleryGrid').innerHTML = '';
            loadImages();
        }

        // Load images
        function loadImages() {
            const gallery = document.getElementById('galleryGrid');
            const loadMoreBtn = document.getElementById('loadMorePhotos');
            
            const imagesToLoad = filteredImages.slice(displayedImages, displayedImages + imagesPerLoad);
            
            imagesToLoad.forEach((image, index) => {
                const galleryItem = createGalleryItem(image, displayedImages + index);
                gallery.appendChild(galleryItem);
            });
            
            displayedImages += imagesToLoad.length;
            
            // Hide load more button if all images are loaded
            if (displayedImages >= filteredImages.length) {
                loadMoreBtn.style.display = 'none';
            } else {
                loadMoreBtn.style.display = 'block';
            }
        }

        // Create gallery item
        function createGalleryItem(image, index) {
            const item = document.createElement('div');
            item.className = 'gallery-item';
            item.innerHTML = `
                <div class="gallery-image">
                    <img src="${image.thumb || image.src}" alt="${image.title}" loading="lazy">
                    <div class="gallery-overlay">
                        <button class="gallery-view-btn" onclick="openLightbox(${index})">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <div class="gallery-info">
                            <h4>${image.title}</h4>
                            <p><i class="fas fa-map-marker-alt"></i> ${image.location}</p>
                        </div>
                    </div>
                </div>
            `;
            
            return item;
        }

        // Setup lightbox
        function setupLightbox() {
            const loadMoreBtn = document.getElementById('loadMorePhotos');
            loadMoreBtn.addEventListener('click', loadImages);
            
            // Close lightbox on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeLightbox();
                }
                if (e.key === 'ArrowLeft') {
                    prevImage();
                }
                if (e.key === 'ArrowRight') {
                    nextImage();
                }
            });
        }

        // Open lightbox
        function openLightbox(index) {
            currentImageIndex = index;
            const image = filteredImages[index];
            
            document.getElementById('lightboxImage').src = image.src;
            document.getElementById('lightboxTitle').textContent = image.title;
            document.getElementById('lightboxDescription').textContent = image.description;
            document.getElementById('lightboxModal').style.display = 'flex';
            
            // Prevent body scroll
            document.body.style.overflow = 'hidden';
        }

        // Close lightbox
        function closeLightbox() {
            document.getElementById('lightboxModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Previous image
        function prevImage() {
            currentImageIndex = currentImageIndex > 0 ? currentImageIndex - 1 : filteredImages.length - 1;
            const image = filteredImages[currentImageIndex];
            
            document.getElementById('lightboxImage').src = image.src;
            document.getElementById('lightboxTitle').textContent = image.title;
            document.getElementById('lightboxDescription').textContent = image.description;
        }

        // Next image
        function nextImage() {
            currentImageIndex = currentImageIndex < filteredImages.length - 1 ? currentImageIndex + 1 : 0;
            const image = filteredImages[currentImageIndex];
            
            document.getElementById('lightboxImage').src = image.src;
            document.getElementById('lightboxTitle').textContent = image.title;
            document.getElementById('lightboxDescription').textContent = image.description;
        }

        // Close lightbox when clicking outside
        document.getElementById('lightboxModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeLightbox();
            }
        });
    </script>
</body>
</html>
