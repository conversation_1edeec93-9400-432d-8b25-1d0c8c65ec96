// Product comparison functionality for a.agrotech

// Comparison state management
class ComparisonManager {
    constructor() {
        this.maxProducts = 3;
        this.selectedProducts = new Set();
        this.comparisonData = [];
        this.init();
    }

    init() {
        this.loadFromStorage();
        this.updateComparisonBar();
        this.bindEvents();
    }

    // Load comparison data from localStorage
    loadFromStorage() {
        const stored = localStorage.getItem('comparisonProducts');
        if (stored) {
            try {
                this.comparisonData = JSON.parse(stored);
                this.selectedProducts = new Set(this.comparisonData.map(p => p.id));
            } catch (error) {
                console.error('Error loading comparison data:', error);
                this.clearComparison();
            }
        }
    }

    // Save comparison data to localStorage
    saveToStorage() {
        localStorage.setItem('comparisonProducts', JSON.stringify(this.comparisonData));
    }

    // Add product to comparison
    addProduct(product) {
        if (this.selectedProducts.size >= this.maxProducts) {
            this.showMessage('Maximum 3 products can be compared at once', 'warning');
            return false;
        }

        if (this.selectedProducts.has(product.id)) {
            this.showMessage('Product already added to comparison', 'info');
            return false;
        }

        this.selectedProducts.add(product.id);
        this.comparisonData.push(product);
        this.saveToStorage();
        this.updateComparisonBar();
        this.updateComparisonButtons();
        this.showMessage(`${product.name} added to comparison`, 'success');
        return true;
    }

    // Remove product from comparison
    removeProduct(productId) {
        this.selectedProducts.delete(productId);
        this.comparisonData = this.comparisonData.filter(p => p.id !== productId);
        this.saveToStorage();
        this.updateComparisonBar();
        this.updateComparisonButtons();
        this.showMessage('Product removed from comparison', 'info');
    }

    // Clear all products from comparison
    clearComparison() {
        this.selectedProducts.clear();
        this.comparisonData = [];
        this.saveToStorage();
        this.updateComparisonBar();
        this.updateComparisonButtons();
    }

    // Check if product is in comparison
    isInComparison(productId) {
        return this.selectedProducts.has(productId);
    }

    // Get comparison count
    getCount() {
        return this.selectedProducts.size;
    }

    // Update comparison bar visibility and content
    updateComparisonBar() {
        const comparisonBar = document.getElementById('comparisonBar');
        if (!comparisonBar) return;

        const count = this.getCount();
        const countElement = comparisonBar.querySelector('.comparison-count');
        const compareBtn = comparisonBar.querySelector('#compareBtn');

        if (countElement) {
            countElement.textContent = count;
        }

        if (compareBtn) {
            compareBtn.disabled = count < 2;
        }

        if (count > 0) {
            comparisonBar.classList.add('active');
        } else {
            comparisonBar.classList.remove('active');
        }
    }

    // Update comparison buttons on product cards
    updateComparisonButtons() {
        const comparisonButtons = document.querySelectorAll('[data-product-id]');
        comparisonButtons.forEach(button => {
            const productId = parseInt(button.dataset.productId);
            const isInComparison = this.isInComparison(productId);
            
            if (isInComparison) {
                button.classList.add('in-comparison');
                button.innerHTML = '<i class="fas fa-check"></i> In Comparison';
            } else {
                button.classList.remove('in-comparison');
                button.innerHTML = '<i class="fas fa-balance-scale"></i> Add to Compare';
            }
        });
    }

    // Navigate to comparison page
    goToComparison() {
        if (this.getCount() < 2) {
            this.showMessage('Please select at least 2 products to compare', 'warning');
            return;
        }

        const productIds = Array.from(this.selectedProducts).join(',');
        window.location.href = `comparison.html?products=${productIds}`;
    }

    // Bind event listeners
    bindEvents() {
        // Compare button in comparison bar
        const compareBtn = document.getElementById('compareBtn');
        if (compareBtn) {
            compareBtn.addEventListener('click', () => this.goToComparison());
        }

        // Clear comparison button
        const clearBtn = document.getElementById('clearComparisonBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearComparison());
        }

        // Product comparison buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.add-to-compare-btn')) {
                e.preventDefault();
                const button = e.target.closest('.add-to-compare-btn');
                const productId = parseInt(button.dataset.productId);
                
                if (this.isInComparison(productId)) {
                    this.removeProduct(productId);
                } else {
                    // Get product data (this would typically come from the page or API)
                    const productData = this.getProductDataFromPage(productId);
                    if (productData) {
                        this.addProduct(productData);
                    }
                }
            }
        });
    }

    // Extract product data from page (fallback method)
    getProductDataFromPage(productId) {
        // Try to find product data in the page
        const productCard = document.querySelector(`[data-product-id="${productId}"]`).closest('.product-card');
        if (!productCard) return null;

        const name = productCard.querySelector('.product-title')?.textContent || 'Unknown Product';
        const brand = productCard.querySelector('.product-brand')?.textContent || 'Unknown Brand';
        const image = productCard.querySelector('.product-image img')?.src || 'images/placeholder-product.jpg';

        return {
            id: productId,
            name: name,
            brand: brand,
            image: image
        };
    }

    // Show message to user
    showMessage(message, type = 'info') {
        // Create or update message element
        let messageEl = document.getElementById('comparisonMessage');
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.id = 'comparisonMessage';
            messageEl.className = 'comparison-message';
            document.body.appendChild(messageEl);
        }

        messageEl.className = `comparison-message ${type}`;
        messageEl.textContent = message;
        messageEl.style.display = 'block';

        // Auto-hide after 3 seconds
        setTimeout(() => {
            messageEl.style.display = 'none';
        }, 3000);
    }
}

// Initialize comparison manager
let comparisonManager;

document.addEventListener('DOMContentLoaded', function() {
    comparisonManager = new ComparisonManager();
});

// Global functions for use in HTML
window.addToComparison = function(productData) {
    if (comparisonManager) {
        return comparisonManager.addProduct(productData);
    }
    return false;
};

window.removeFromComparison = function(productId) {
    if (comparisonManager) {
        comparisonManager.removeProduct(productId);
    }
};

window.clearAllComparison = function() {
    if (comparisonManager) {
        comparisonManager.clearComparison();
    }
};

window.goToComparison = function() {
    if (comparisonManager) {
        comparisonManager.goToComparison();
    }
};

// Enhanced product card rendering with comparison functionality
function renderProductWithComparison(product, container) {
    const isInComparison = comparisonManager ? comparisonManager.isInComparison(product.id) : false;
    
    const productCard = document.createElement('div');
    productCard.className = 'product-card';
    productCard.innerHTML = `
        <div class="product-image">
            <img src="${product.image}" alt="${product.name}" loading="lazy">
            ${product.badge ? `<span class="product-badge">${product.badge}</span>` : ''}
        </div>
        <div class="product-info">
            <div class="product-brand">${product.brand}</div>
            <h3 class="product-title">${product.name}</h3>
            <p class="product-description">${product.description}</p>
            <ul class="product-features">
                ${product.features.map(feature => `
                    <li><i class="fas fa-check"></i> ${feature}</li>
                `).join('')}
            </ul>
            <div class="product-actions">
                <a href="product-detail.html?id=${product.id}" class="btn btn-primary btn-small">
                    View Details
                </a>
                <button class="btn btn-secondary btn-small" onclick="requestQuote(${product.id})">
                    Get Quote
                </button>
                <button class="btn btn-outline btn-small add-to-compare-btn ${isInComparison ? 'in-comparison' : ''}" 
                        data-product-id="${product.id}">
                    <i class="fas fa-${isInComparison ? 'check' : 'balance-scale'}"></i>
                    ${isInComparison ? 'In Comparison' : 'Add to Compare'}
                </button>
            </div>
        </div>
    `;
    
    container.appendChild(productCard);
}

// Comparison-specific CSS (to be added to main CSS file)
const comparisonCSS = `
.comparison-message {
    position: fixed;
    top: 100px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    display: none;
    animation: slideInRight 0.3s ease-out;
}

.comparison-message.success {
    background-color: #4CAF50;
}

.comparison-message.warning {
    background-color: #FF9800;
}

.comparison-message.info {
    background-color: #2196F3;
}

.comparison-message.error {
    background-color: #F44336;
}

.add-to-compare-btn.in-comparison {
    background-color: var(--success);
    border-color: var(--success);
    color: var(--white);
}

.add-to-compare-btn.in-comparison:hover {
    background-color: #388E3C;
    border-color: #388E3C;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
`;

// Inject comparison CSS
const style = document.createElement('style');
style.textContent = comparisonCSS;
document.head.appendChild(style);

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ComparisonManager };
}
