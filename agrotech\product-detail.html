<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="pageTitle">Product Details - a.agrotech</title>
    <meta name="description" id="pageDescription" content="Detailed specifications and information about agricultural equipment">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.html" class="logo">
                        <i class="fas fa-seedling"></i>
                        <span>a.agrotech</span>
                    </a>
                </div>
                
                <!-- Mobile Menu Toggle -->
                <button class="mobile-menu-toggle" aria-label="Toggle menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                
                <!-- Navigation Menu -->
                <div class="nav-menu">
                    <ul class="nav-links">
                        <li><a href="index.html">Home</a></li>
                        <li class="dropdown">
                            <a href="products.html">Products <i class="fas fa-chevron-down"></i></a>
                            <div class="dropdown-menu">
                                <div class="dropdown-section">
                                    <h4>Shop by Category</h4>
                                    <a href="products.html?category=tillage">Tillage Equipment</a>
                                    <a href="products.html?category=sowing">Sowing & Planting</a>
                                    <a href="products.html?category=protection">Crop Protection</a>
                                    <a href="products.html?category=harvesting">Harvesting</a>
                                </div>
                                <div class="dropdown-section">
                                    <h4>Shop by Brand</h4>
                                    <a href="products.html?brand=mahindra">Mahindra</a>
                                    <a href="products.html?brand=sonalika">Sonalika</a>
                                    <a href="products.html?brand=johndeere">John Deere</a>
                                </div>
                                <div class="dropdown-section">
                                    <h4>Shop by Crop</h4>
                                    <a href="products.html?crop=paddy">Paddy Solutions</a>
                                    <a href="products.html?crop=wheat">Wheat Solutions</a>
                                    <a href="products.html?crop=sugarcane">Sugarcane Solutions</a>
                                </div>
                            </div>
                        </li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="photo-gallery.html">Gallery</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                    
                    <!-- Search Bar -->
                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="Search equipment..." id="searchInput">
                        <button class="search-btn" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                        <div class="search-suggestions" id="searchSuggestions"></div>
                    </div>
                    
                    <!-- Contact Info -->
                    <div class="header-contact">
                        <a href="tel:1800-123-4567" class="toll-free">
                            <i class="fas fa-phone"></i>
                            <span>1800-123-4567</span>
                        </a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Breadcrumb -->
    <section class="breadcrumb-section">
        <div class="container">
            <nav class="breadcrumb">
                <a href="index.html">Home</a>
                <span>/</span>
                <a href="products.html">Products</a>
                <span>/</span>
                <span id="productBreadcrumb">Product Details</span>
            </nav>
        </div>
    </section>

    <!-- Product Detail Section -->
    <section class="product-detail-section">
        <div class="container">
            <div class="product-detail-layout">
                <!-- Product Images -->
                <div class="product-images">
                    <div class="main-image">
                        <img id="mainProductImage" src="images/placeholder-product.jpg" alt="Product Image" loading="lazy">
                        <div class="image-badges" id="imageBadges">
                            <!-- Badges will be added dynamically -->
                        </div>
                        <button class="video-play-btn" id="videoPlayBtn" style="display: none;">
                            <i class="fas fa-play"></i>
                            <span>Watch Demo</span>
                        </button>
                    </div>
                    <div class="thumbnail-images" id="thumbnailImages">
                        <!-- Thumbnail images will be added dynamically -->
                    </div>
                </div>
                
                <!-- Product Info -->
                <div class="product-info">
                    <div class="product-header">
                        <div class="product-brand" id="productBrand">Brand</div>
                        <h1 class="product-title" id="productTitle">Product Name</h1>
                        <div class="product-rating">
                            <div class="stars" id="productStars">
                                <!-- Stars will be added dynamically -->
                            </div>
                            <span class="rating-text" id="ratingText">0.0 (0 reviews)</span>
                        </div>
                        <div class="product-price" id="productPrice">Price on Request</div>
                    </div>
                    
                    <div class="product-description" id="productDescription">
                        <!-- Description will be added dynamically -->
                    </div>
                    
                    <div class="product-features" id="productFeatures">
                        <h3>Key Features</h3>
                        <ul>
                            <!-- Features will be added dynamically -->
                        </ul>
                    </div>
                    
                    <div class="product-actions">
                        <button class="btn btn-primary" id="getQuoteBtn">
                            <i class="fas fa-file-alt"></i>
                            Get Quote from Dealer
                        </button>
                        <button class="btn btn-secondary" id="contactDealerBtn">
                            <i class="fas fa-phone"></i>
                            Contact Local Dealer
                        </button>
                        <button class="btn btn-outline" id="addToCompareBtn">
                            <i class="fas fa-balance-scale"></i>
                            Add to Compare
                        </button>
                    </div>
                    
                    <div class="product-meta">
                        <div class="meta-item">
                            <i class="fas fa-seedling"></i>
                            <span>Suitable for: <span id="suitableCrops">Various crops</span></span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Available across India through authorized dealers</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>Warranty and after-sales support included</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Details Tabs -->
    <section class="product-tabs-section">
        <div class="container">
            <div class="tabs-container">
                <div class="tabs-nav">
                    <button class="tab-btn active" data-tab="description">Description</button>
                    <button class="tab-btn" data-tab="specifications">Technical Specifications</button>
                    <button class="tab-btn" data-tab="reviews">Customer Reviews</button>
                    <button class="tab-btn" data-tab="financing">Financing Options</button>
                </div>
                
                <div class="tabs-content">
                    <!-- Description Tab -->
                    <div class="tab-panel active" id="description">
                        <div class="tab-content">
                            <h3>Product Description</h3>
                            <div id="detailedDescription">
                                <!-- Detailed description will be loaded here -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Specifications Tab -->
                    <div class="tab-panel" id="specifications">
                        <div class="tab-content">
                            <h3>Technical Specifications</h3>
                            <div class="specifications-table" id="specificationsTable">
                                <!-- Specifications table will be loaded here -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Reviews Tab -->
                    <div class="tab-panel" id="reviews">
                        <div class="tab-content">
                            <h3>Customer Reviews</h3>
                            <div class="reviews-summary">
                                <div class="rating-overview">
                                    <div class="overall-rating">
                                        <span class="rating-number" id="overallRating">0.0</span>
                                        <div class="rating-stars" id="overallStars"></div>
                                        <span class="total-reviews" id="totalReviews">0 reviews</span>
                                    </div>
                                </div>
                            </div>
                            <div class="reviews-list" id="reviewsList">
                                <!-- Reviews will be loaded here -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Financing Tab -->
                    <div class="tab-panel" id="financing">
                        <div class="tab-content">
                            <h3>Financing Options</h3>
                            <div class="financing-options">
                                <div class="financing-card">
                                    <div class="financing-icon">
                                        <i class="fas fa-university"></i>
                                    </div>
                                    <h4>Bank Loans</h4>
                                    <p>Get competitive interest rates from leading banks. Loan amount up to 85% of equipment value.</p>
                                    <ul>
                                        <li>Interest rates starting from 8.5%</li>
                                        <li>Loan tenure up to 7 years</li>
                                        <li>Minimal documentation</li>
                                    </ul>
                                </div>
                                <div class="financing-card">
                                    <div class="financing-icon">
                                        <i class="fas fa-hand-holding-usd"></i>
                                    </div>
                                    <h4>Government Subsidies</h4>
                                    <p>Avail government subsidies and schemes for agricultural equipment purchase.</p>
                                    <ul>
                                        <li>Up to 50% subsidy for eligible farmers</li>
                                        <li>Special schemes for SC/ST farmers</li>
                                        <li>State-specific subsidy programs</li>
                                    </ul>
                                </div>
                                <div class="financing-card">
                                    <div class="financing-icon">
                                        <i class="fas fa-credit-card"></i>
                                    </div>
                                    <h4>Manufacturer Financing</h4>
                                    <p>Direct financing options from equipment manufacturers with attractive terms.</p>
                                    <ul>
                                        <li>Zero down payment options</li>
                                        <li>Flexible EMI plans</li>
                                        <li>Quick approval process</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Products -->
    <section class="related-products">
        <div class="container">
            <div class="section-header">
                <h2>Related Products</h2>
                <p>You might also be interested in these products</p>
            </div>
            <div class="product-grid" id="relatedProducts">
                <!-- Related products will be loaded dynamically -->
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Ready to Purchase This Equipment?</h2>
                <p>Connect with verified dealers in your area for the best prices and service</p>
                <div class="cta-buttons">
                    <button class="btn btn-primary" onclick="requestQuote()">
                        <i class="fas fa-file-alt"></i>
                        Request Quote
                    </button>
                    <a href="contact.html" class="btn btn-secondary">
                        <i class="fas fa-phone"></i>
                        Talk to Expert
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <a href="index.html" class="logo">
                            <i class="fas fa-seedling"></i>
                            <span>a.agrotech</span>
                        </a>
                        <p>Empowering Indian farmers with the right agricultural equipment and trusted dealer connections.</p>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="photo-gallery.html">Gallery</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Categories</h4>
                    <ul>
                        <li><a href="products.html?category=tillage">Tillage Equipment</a></li>
                        <li><a href="products.html?category=sowing">Sowing & Planting</a></li>
                        <li><a href="products.html?category=protection">Crop Protection</a></li>
                        <li><a href="products.html?category=harvesting">Harvesting</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-phone"></i> 1800-123-4567 (Toll Free)</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> New Delhi, India</p>
                    </div>
                    <div class="social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="#" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 a.agrotech. All rights reserved. | <a href="#">Privacy Policy</a> | <a href="#">Terms of Service</a></p>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Float Button -->
    <a href="https://wa.me/************" class="whatsapp-float" target="_blank" aria-label="Chat on WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Video Modal -->
    <div class="modal" id="videoModal">
        <div class="modal-content video-modal">
            <div class="modal-header">
                <h3>Product Demonstration</h3>
                <button class="modal-close" onclick="closeVideoModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="video-container">
                    <iframe id="productVideo" src="" frameborder="0" allowfullscreen></iframe>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/search.js"></script>
    <script>
        // Product detail page functionality
        let currentProduct = null;
        
        document.addEventListener('DOMContentLoaded', function() {
            loadProductDetails();
            initTabs();
            initImageGallery();
        });
        
        // Load product details from URL parameter
        async function loadProductDetails() {
            const urlParams = new URLSearchParams(window.location.search);
            const productId = urlParams.get('id');
            
            if (!productId) {
                window.location.href = 'products.html';
                return;
            }
            
            try {
                // In a real application, this would be an API call
                const response = await fetch('data/products.json');
                const data = await response.json();
                const product = data.products.find(p => p.id == productId);
                
                if (!product) {
                    window.location.href = 'products.html';
                    return;
                }
                
                currentProduct = product;
                renderProductDetails(product);
                loadRelatedProducts(product);
            } catch (error) {
                console.error('Error loading product details:', error);
                // Fallback to mock data
                loadMockProductDetails(productId);
            }
        }
        
        // Render product details
        function renderProductDetails(product) {
            // Update page title and meta
            document.getElementById('pageTitle').textContent = `${product.name} - a.agrotech`;
            document.getElementById('pageDescription').content = product.description;
            
            // Update breadcrumb
            document.getElementById('productBreadcrumb').textContent = product.name;
            
            // Update product info
            document.getElementById('productBrand').textContent = product.brand;
            document.getElementById('productTitle').textContent = product.name;
            document.getElementById('productDescription').textContent = product.description;
            document.getElementById('productPrice').textContent = product.priceRange || 'Price on Request';
            
            // Update rating
            renderRating(product.rating, product.reviews);
            
            // Update features
            renderFeatures(product.features);
            
            // Update images
            renderImages(product);
            
            // Update suitable crops
            document.getElementById('suitableCrops').textContent = product.crop.join(', ');
            
            // Update tabs content
            renderTabsContent(product);
            
            // Add badges
            if (product.badge) {
                document.getElementById('imageBadges').innerHTML = `<span class="product-badge">${product.badge}</span>`;
            }
            
            // Setup video button
            if (product.videoUrl) {
                const videoBtn = document.getElementById('videoPlayBtn');
                videoBtn.style.display = 'flex';
                videoBtn.onclick = () => openVideoModal(product.videoUrl);
            }
        }
        
        // Render rating stars
        function renderRating(rating, reviewCount) {
            const starsContainer = document.getElementById('productStars');
            const ratingText = document.getElementById('ratingText');
            
            let starsHTML = '';
            for (let i = 1; i <= 5; i++) {
                if (i <= rating) {
                    starsHTML += '<i class="fas fa-star"></i>';
                } else if (i - 0.5 <= rating) {
                    starsHTML += '<i class="fas fa-star-half-alt"></i>';
                } else {
                    starsHTML += '<i class="far fa-star"></i>';
                }
            }
            
            starsContainer.innerHTML = starsHTML;
            ratingText.textContent = `${rating} (${reviewCount} reviews)`;
        }
        
        // Render features
        function renderFeatures(features) {
            const featuresContainer = document.getElementById('productFeatures').querySelector('ul');
            featuresContainer.innerHTML = features.map(feature => 
                `<li><i class="fas fa-check"></i> ${feature}</li>`
            ).join('');
        }
        
        // Render images
        function renderImages(product) {
            const mainImage = document.getElementById('mainProductImage');
            const thumbnailContainer = document.getElementById('thumbnailImages');
            
            mainImage.src = product.image;
            mainImage.alt = product.name;
            
            if (product.images && product.images.length > 1) {
                thumbnailContainer.innerHTML = product.images.map((img, index) => 
                    `<img src="${img}" alt="${product.name} ${index + 1}" 
                          onclick="changeMainImage('${img}')" 
                          class="${index === 0 ? 'active' : ''}">`
                ).join('');
            }
        }
        
        // Change main image
        function changeMainImage(imageSrc) {
            document.getElementById('mainProductImage').src = imageSrc;
            
            // Update active thumbnail
            document.querySelectorAll('#thumbnailImages img').forEach(img => {
                img.classList.toggle('active', img.src.includes(imageSrc));
            });
        }
        
        // Initialize tabs
        function initTabs() {
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabPanels = document.querySelectorAll('.tab-panel');
            
            tabButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const targetTab = this.dataset.tab;
                    
                    // Remove active class from all buttons and panels
                    tabButtons.forEach(b => b.classList.remove('active'));
                    tabPanels.forEach(p => p.classList.remove('active'));
                    
                    // Add active class to clicked button and corresponding panel
                    this.classList.add('active');
                    document.getElementById(targetTab).classList.add('active');
                });
            });
        }
        
        // Render tabs content
        function renderTabsContent(product) {
            // Description tab
            document.getElementById('detailedDescription').innerHTML = `
                <p>${product.description}</p>
                <h4>Key Benefits:</h4>
                <ul>
                    ${product.features.map(feature => `<li>${feature}</li>`).join('')}
                </ul>
            `;
            
            // Specifications tab
            if (product.specifications) {
                const specsTable = document.getElementById('specificationsTable');
                specsTable.innerHTML = `
                    <table class="specs-table">
                        ${Object.entries(product.specifications).map(([key, value]) => 
                            `<tr><td>${key}</td><td>${value}</td></tr>`
                        ).join('')}
                    </table>
                `;
            }
            
            // Reviews tab
            document.getElementById('overallRating').textContent = product.rating;
            document.getElementById('totalReviews').textContent = `${product.reviews} reviews`;
            renderRating(product.rating, product.reviews, 'overallStars');
        }
        
        // Initialize image gallery
        function initImageGallery() {
            // Image gallery functionality can be enhanced here
        }
        
        // Load related products
        function loadRelatedProducts(product) {
            // Mock related products for now
            const relatedContainer = document.getElementById('relatedProducts');
            // This would typically fetch related products from API
        }
        
        // Open video modal
        function openVideoModal(videoUrl) {
            const modal = document.getElementById('videoModal');
            const iframe = document.getElementById('productVideo');
            iframe.src = videoUrl;
            modal.style.display = 'flex';
        }
        
        // Close video modal
        function closeVideoModal() {
            const modal = document.getElementById('videoModal');
            const iframe = document.getElementById('productVideo');
            iframe.src = '';
            modal.style.display = 'none';
        }
        
        // Request quote function
        function requestQuote() {
            if (currentProduct) {
                localStorage.setItem('quoteProductId', currentProduct.id);
                window.location.href = 'contact.html?action=quote&product=' + currentProduct.id;
            }
        }
        
        // Load mock product details (fallback)
        function loadMockProductDetails(productId) {
            const mockProduct = {
                id: productId,
                name: 'Sample Agricultural Equipment',
                brand: 'Sample Brand',
                description: 'This is a sample product description.',
                features: ['Feature 1', 'Feature 2', 'Feature 3'],
                rating: 4.0,
                reviews: 50,
                crop: ['wheat', 'paddy'],
                priceRange: 'Price on Request',
                image: 'images/placeholder-product.jpg'
            };
            
            currentProduct = mockProduct;
            renderProductDetails(mockProduct);
        }
    </script>
</body>
</html>
