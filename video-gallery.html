<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Gallery - a.agrotech | Agricultural Equipment Demonstrations</title>
    <meta name="description" content="Watch detailed video demonstrations of agricultural equipment in action. See how modern farming equipment works in real conditions.">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.html" class="logo">
                        <i class="fas fa-seedling"></i>
                        <span>a.agrotech</span>
                    </a>
                </div>
                
                <!-- Mobile Menu Toggle -->
                <button class="mobile-menu-toggle" aria-label="Toggle menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                
                <!-- Navigation Menu -->
                <div class="nav-menu">
                    <ul class="nav-links">
                        <li><a href="index.html">Home</a></li>
                        <li class="dropdown">
                            <a href="products.html">Products <i class="fas fa-chevron-down"></i></a>
                            <div class="dropdown-menu">
                                <div class="dropdown-section">
                                    <h4>Shop by Category</h4>
                                    <a href="products.html?category=tillage">Tillage Equipment</a>
                                    <a href="products.html?category=sowing">Sowing & Planting</a>
                                    <a href="products.html?category=protection">Crop Protection</a>
                                    <a href="products.html?category=harvesting">Harvesting</a>
                                </div>
                                <div class="dropdown-section">
                                    <h4>Shop by Brand</h4>
                                    <a href="products.html?brand=mahindra">Mahindra</a>
                                    <a href="products.html?brand=sonalika">Sonalika</a>
                                    <a href="products.html?brand=johndeere">John Deere</a>
                                </div>
                                <div class="dropdown-section">
                                    <h4>Shop by Crop</h4>
                                    <a href="products.html?crop=paddy">Paddy Solutions</a>
                                    <a href="products.html?crop=wheat">Wheat Solutions</a>
                                    <a href="products.html?crop=sugarcane">Sugarcane Solutions</a>
                                </div>
                            </div>
                        </li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="photo-gallery.html" class="active">Gallery</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                    
                    <!-- Search Bar -->
                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="Search equipment..." id="searchInput">
                        <button class="search-btn" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                        <div class="search-suggestions" id="searchSuggestions"></div>
                    </div>
                    
                    <!-- Contact Info -->
                    <div class="header-contact">
                        <a href="tel:1800-123-4567" class="toll-free">
                            <i class="fas fa-phone"></i>
                            <span>1800-123-4567</span>
                        </a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content">
                <h1>Video Gallery</h1>
                <p>Watch agricultural equipment demonstrations and farmer success stories</p>
                <nav class="breadcrumb">
                    <a href="index.html">Home</a>
                    <span>/</span>
                    <a href="photo-gallery.html">Gallery</a>
                    <span>/</span>
                    <span>Videos</span>
                </nav>
            </div>
        </div>
    </section>

    <!-- Video Gallery Navigation -->
    <section class="gallery-nav">
        <div class="container">
            <div class="gallery-filters">
                <button class="filter-btn active" data-filter="all">All Videos</button>
                <button class="filter-btn" data-filter="demonstrations">Equipment Demos</button>
                <button class="filter-btn" data-filter="tutorials">How-to Tutorials</button>
                <button class="filter-btn" data-filter="testimonials">Farmer Stories</button>
                <button class="filter-btn" data-filter="reviews">Product Reviews</button>
            </div>
        </div>
    </section>

    <!-- Video Gallery -->
    <section class="video-gallery-section">
        <div class="container">
            <div class="video-grid" id="videoGrid">
                <!-- Video items will be loaded dynamically -->
            </div>
            
            <!-- Load More Button -->
            <div class="load-more-container">
                <button class="btn btn-outline" id="loadMoreVideos">
                    <i class="fas fa-plus"></i>
                    Load More Videos
                </button>
            </div>
        </div>
    </section>

    <!-- Photo Gallery Link -->
    <section class="photo-gallery-link">
        <div class="container">
            <div class="photo-link-card">
                <div class="photo-link-content">
                    <div class="photo-link-icon">
                        <i class="fas fa-camera"></i>
                    </div>
                    <div class="photo-link-text">
                        <h3>Browse Photo Gallery</h3>
                        <p>View high-quality photos of agricultural equipment in action across Indian farms</p>
                    </div>
                </div>
                <a href="photo-gallery.html" class="btn btn-primary">
                    <i class="fas fa-images"></i>
                    View Photo Gallery
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <a href="index.html" class="logo">
                            <i class="fas fa-seedling"></i>
                            <span>a.agrotech</span>
                        </a>
                        <p>Empowering Indian farmers with the right agricultural equipment and trusted dealer connections.</p>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="photo-gallery.html">Gallery</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Categories</h4>
                    <ul>
                        <li><a href="products.html?category=tillage">Tillage Equipment</a></li>
                        <li><a href="products.html?category=sowing">Sowing & Planting</a></li>
                        <li><a href="products.html?category=protection">Crop Protection</a></li>
                        <li><a href="products.html?category=harvesting">Harvesting</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-phone"></i> 1800-123-4567 (Toll Free)</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> New Delhi, India</p>
                    </div>
                    <div class="social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="#" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 a.agrotech. All rights reserved. | <a href="#">Privacy Policy</a> | <a href="#">Terms of Service</a></p>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Float Button -->
    <a href="https://wa.me/************" class="whatsapp-float" target="_blank" aria-label="Chat on WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Video Modal -->
    <div class="modal" id="videoModal">
        <div class="modal-content video-modal">
            <div class="modal-header">
                <h3 id="videoModalTitle">Video</h3>
                <button class="modal-close" onclick="closeVideoModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="video-container">
                    <iframe id="modalVideo" src="" frameborder="0" allowfullscreen></iframe>
                </div>
                <div class="video-info">
                    <p id="videoModalDescription"></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/search.js"></script>
    <script>
        // Video gallery data
        const videoData = [
            {
                id: 1,
                title: 'Mahindra 575 DI Tractor Complete Review',
                description: 'Comprehensive review of Mahindra 575 DI tractor including features, performance, and farmer feedback',
                thumbnail: 'images/video-thumbs/mahindra-575-review.jpg',
                videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
                duration: '12:45',
                category: 'reviews',
                views: '25,432'
            },
            {
                id: 2,
                title: 'Rotavator Operation and Maintenance',
                description: 'Learn how to operate and maintain your rotavator for optimal performance',
                thumbnail: 'images/video-thumbs/rotavator-tutorial.jpg',
                videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
                duration: '8:30',
                category: 'tutorials',
                views: '18,765'
            },
            {
                id: 3,
                title: 'Farmer Success Story - Punjab',
                description: 'How modern equipment transformed farming for Rajesh Kumar in Punjab',
                thumbnail: 'images/video-thumbs/farmer-success-punjab.jpg',
                videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
                duration: '6:20',
                category: 'testimonials',
                views: '32,156'
            },
            {
                id: 4,
                title: 'Combine Harvester Field Demonstration',
                description: 'Watch the latest combine harvester in action during wheat harvest season',
                thumbnail: 'images/video-thumbs/combine-demo.jpg',
                videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
                duration: '15:22',
                category: 'demonstrations',
                views: '41,289'
            },
            {
                id: 5,
                title: 'Seed Drill Precision Sowing Demo',
                description: 'See how precision seed drills ensure optimal seed placement and spacing',
                thumbnail: 'images/video-thumbs/seed-drill-demo.jpg',
                videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
                duration: '9:15',
                category: 'demonstrations',
                views: '15,643'
            },
            {
                id: 6,
                title: 'Tractor Maintenance Tips',
                description: 'Essential maintenance tips to keep your tractor running efficiently',
                thumbnail: 'images/video-thumbs/tractor-maintenance.jpg',
                videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
                duration: '11:30',
                category: 'tutorials',
                views: '22,987'
            }
        ];

        let filteredVideos = [...videoData];
        let displayedVideos = 0;
        const videosPerLoad = 6;

        // Initialize video gallery
        document.addEventListener('DOMContentLoaded', function() {
            initVideoGallery();
            setupVideoFilters();
        });

        // Initialize video gallery
        function initVideoGallery() {
            loadVideos();
        }

        // Setup filter buttons
        function setupVideoFilters() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            
            filterButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    // Remove active class from all buttons
                    filterButtons.forEach(b => b.classList.remove('active'));
                    
                    // Add active class to clicked button
                    this.classList.add('active');
                    
                    // Filter videos
                    const filter = this.dataset.filter;
                    filterVideos(filter);
                });
            });
        }

        // Filter videos
        function filterVideos(category) {
            if (category === 'all') {
                filteredVideos = [...videoData];
            } else {
                filteredVideos = videoData.filter(video => video.category === category);
            }
            
            displayedVideos = 0;
            document.getElementById('videoGrid').innerHTML = '';
            loadVideos();
        }

        // Load videos
        function loadVideos() {
            const videoGrid = document.getElementById('videoGrid');
            const loadMoreBtn = document.getElementById('loadMoreVideos');
            
            const videosToLoad = filteredVideos.slice(displayedVideos, displayedVideos + videosPerLoad);
            
            videosToLoad.forEach(video => {
                const videoItem = createVideoItem(video);
                videoGrid.appendChild(videoItem);
            });
            
            displayedVideos += videosToLoad.length;
            
            // Hide load more button if all videos are loaded
            if (displayedVideos >= filteredVideos.length) {
                loadMoreBtn.style.display = 'none';
            } else {
                loadMoreBtn.style.display = 'block';
            }
        }

        // Create video item
        function createVideoItem(video) {
            const item = document.createElement('div');
            item.className = 'video-item';
            item.innerHTML = `
                <div class="video-thumbnail" onclick="openVideoModal('${video.videoUrl}', '${video.title}', '${video.description}')">
                    <img src="${video.thumbnail}" alt="${video.title}" loading="lazy">
                    <div class="video-overlay">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="video-duration">${video.duration}</div>
                    </div>
                </div>
                <div class="video-info">
                    <h3>${video.title}</h3>
                    <p>${video.description}</p>
                    <div class="video-meta">
                        <span class="video-views">
                            <i class="fas fa-eye"></i>
                            ${video.views} views
                        </span>
                    </div>
                </div>
            `;
            
            return item;
        }

        // Open video modal
        function openVideoModal(videoUrl, title, description) {
            document.getElementById('modalVideo').src = videoUrl;
            document.getElementById('videoModalTitle').textContent = title;
            document.getElementById('videoModalDescription').textContent = description;
            document.getElementById('videoModal').style.display = 'flex';
        }

        // Close video modal
        function closeVideoModal() {
            document.getElementById('videoModal').style.display = 'none';
            document.getElementById('modalVideo').src = '';
        }

        // Load more videos
        document.getElementById('loadMoreVideos').addEventListener('click', loadVideos);

        // Close modal when clicking outside
        document.getElementById('videoModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeVideoModal();
            }
        });
    </script>
</body>
</html>
